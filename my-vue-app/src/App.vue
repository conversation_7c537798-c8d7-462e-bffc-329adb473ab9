<script setup>
import { ref } from 'vue'

const isDarkMode = ref(false)
const currentLanguage = ref('中文')

const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value
}

const toggleLanguage = () => {
  currentLanguage.value = currentLanguage.value === '中文' ? 'English' : '中文'
}

const newsItems = [
  {
    date: '2025-04-10',
    title: '币安保本赚币上线RIF定期产品',
    content: '币安保本赚币平台将推出Rootstock Infrastructure Framework（RIF）定期产品专属活动！活动期间申购RIF定期产品的用户，享最高9.9%*年化收益率。'
  },
  {
    date: '2025-04-10',
    title: '关于调整现货交易对最小数量波动的公告',
    content: '为提升市场的有效流动性和交易体验，币安将分别于2025年04月17日15:00（东八区时间）前，完成部分现货交易对的最小数量波动（单位变动的最小值）调整，'
  },
  {
    date: '2025-04-09',
    title: '币安将推出LDUSDT合约交易，并提供年化回报率（APR）奖励',
    content: '币安合约即将推出新的收益型保证金资产——LDUSDT。用户可以通过该新型资产将其USDT保本赚币的活期产品资产兑换为LDUSDT，并将其用作U本位合约的保证金。此外，持有LDUSDT的用户将能够从保本赚币活期产品中继续获得实时的年化回报率（APR）奖励。'
  }
]
</script>

<template>
  <div class="app" :class="{ 'dark-mode': isDarkMode }">
    <!-- Navigation Header -->
    <header class="navbar">
      <div class="nav-container">
        <div class="nav-left">
          <div class="logo">
            <span class="logo-text">OpenQuant</span>
          </div>
          <nav class="nav-menu">
            <a href="#" class="nav-link">首页</a>
            <a href="#" class="nav-link">团队介绍</a>
            <a href="#" class="nav-link">价差分析</a>
            <a href="#" class="nav-link">可开分析</a>
            <a href="#" class="nav-link">API文档</a>
          </nav>
        </div>
        <div class="nav-right">
          <button @click="toggleDarkMode" class="theme-toggle">🌙</button>
          <button @click="toggleLanguage" class="language-toggle">🌐</button>
          <button class="login-btn">登录</button>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-content">
        <h1 class="hero-title">OPENQUANT</h1>
        <h2 class="hero-subtitle">开启非凡量化世界者</h2>
        <p class="hero-description">交易策略开发、量化学习资源、策略出租出售</p>
        <div class="hero-actions">
          <button class="cta-button">免费试用</button>
          <button class="video-button">▶</button>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features">
      <div class="container">
        <div class="feature-grid">
          <div class="feature-item">
            <div class="feature-icon">📝</div>
            <h3>多文档在线编辑</h3>
            <div class="feature-images">
              <div class="feature-img-placeholder">📊</div>
              <div class="feature-img-placeholder">📈</div>
            </div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">🔒</div>
            <h3>数据双重加密</h3>
            <p>双重加密机制确保了数据不泄露，即便数据被</p>
            <div class="feature-images">
              <div class="feature-img-placeholder">🛡️</div>
              <div class="feature-img-placeholder">🔐</div>
            </div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">💻</div>
            <h3>跨平台兼容性</h3>
            <p>策略支持多文件夹多文件</p>
            <div class="feature-images">
              <div class="feature-img-placeholder">📱</div>
              <div class="feature-img-placeholder">💻</div>
            </div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">👥</div>
            <h3>实时协作</h3>
            <p>多位用户可以同时编辑同一文档或代码，即时看到彼此的更改，极大地提升了团队协作的效率和灵活性</p>
            <div class="feature-images">
              <div class="feature-img-placeholder">🤝</div>
              <div class="feature-img-placeholder">⚡</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Programming Languages Section -->
    <section class="languages">
      <div class="container">
        <h2>支持丰富的编程语言</h2>
        <div class="language-grid">
          <div class="language-item">
            <div class="language-icon">🐍</div>
            <h3>Python</h3>
            <p>Python</p>
          </div>
          <div class="language-item">
            <div class="language-icon">⚡</div>
            <h3>C++</h3>
            <p>C++</p>
          </div>
          <div class="language-item">
            <div class="language-icon">📗</div>
            <h3>NodeJs</h3>
            <p>NodeJs</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Special Functions Section -->
    <section class="special-functions">
      <div class="container">
        <h2>特色功能</h2>
        <div class="functions-grid">
          <div class="function-item">
            <div class="function-header">
              <span class="function-label">Function 1</span>
            </div>
            <div class="function-icon">📊</div>
            <h3>实盘管理</h3>
            <p>实盘监控 自定义列数据 便捷指令 批量操作</p>
          </div>

          <div class="function-item">
            <div class="function-header">
              <span class="function-label">Function 2</span>
            </div>
            <div class="function-icon">⚙️</div>
            <h3>策略管理</h3>
            <p>多语言支持 代码加密上传 多文件 在线编辑</p>
          </div>

          <div class="function-item">
            <div class="function-header">
              <span class="function-label">Function 3</span>
            </div>
            <div class="function-icon">👥</div>
            <h3>团队管理</h3>
            <p>策略共享 服务器共享 实盘共享 统一操控</p>
          </div>

          <div class="function-item">
            <div class="function-header">
              <span class="function-label">Function 4</span>
            </div>
            <div class="function-icon">✨</div>
            <h3>更多功能</h3>
            <p>敬请期待</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Exchanges Section -->
    <section class="exchanges">
      <div class="container">
        <h2>全面的交易所对接</h2>
        <div class="exchange-grid">
          <div class="exchange-item">
            <div class="exchange-logo">🔥</div>
            <span>Huobi</span>
          </div>
          <div class="exchange-item">
            <div class="exchange-logo">⚡</div>
            <span>Binance</span>
          </div>
          <div class="exchange-item">
            <div class="exchange-logo">🚀</div>
            <span>Okex</span>
          </div>
          <div class="exchange-item">
            <div class="exchange-logo">🌟</div>
            <span>Gate</span>
          </div>
          <div class="exchange-item">
            <div class="exchange-logo">💎</div>
            <span>Mexc</span>
          </div>
          <div class="exchange-item">
            <div class="exchange-logo">⚡</div>
            <span>BitMEX</span>
          </div>
          <div class="exchange-item">
            <div class="exchange-logo">🔷</div>
            <span>Bybit</span>
          </div>
        </div>
      </div>
    </section>

    <!-- News Section -->
    <section class="news">
      <div class="container">
        <h2>热点资讯</h2>
        <div class="news-list">
          <div v-for="(item, index) in newsItems" :key="index" class="news-item">
            <div class="news-date">{{ item.date }}</div>
            <h3 class="news-title">{{ item.title }}</h3>
            <p class="news-content">{{ item.content }}</p>
            <button class="news-btn">查看 →</button>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-links">
          <a href="#">关于我们</a>
          <span>|</span>
          <a href="#">服务条款</a>
          <span>|</span>
          <a href="#">隐私政策</a>
          <span>|</span>
          <a href="#">免责声明</a>
        </div>
        <div class="footer-disclaimer">
          <p>市场有风险，投资需谨慎 | 数据仅供参考，不构成投资建议</p>
          <p>Copyright © 2018-2024 OpenQuant Technology Co., Ltd. All Rights Reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
  transition: all 0.3s ease;
}

.app.dark-mode {
  background-color: #1a1a1a;
  color: #fff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #eee;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 1rem 0;
}

.dark-mode .navbar {
  background: rgba(26, 26, 26, 0.95);
  border-bottom: 1px solid #333;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2563eb;
}

.nav-menu {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  text-decoration: none;
  color: inherit;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #2563eb;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.theme-toggle,
.language-toggle {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.3s ease;
}

.theme-toggle:hover,
.language-toggle:hover {
  background-color: #f3f4f6;
}

.dark-mode .theme-toggle:hover,
.dark-mode .language-toggle:hover {
  background-color: #374151;
}

.login-btn {
  background: #2563eb;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.login-btn:hover {
  background: #1d4ed8;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rem 2rem 4rem;
  text-align: center;
  margin-top: 80px;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  letter-spacing: 2px;
}

.hero-subtitle {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  font-weight: 300;
}

.hero-description {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  align-items: center;
}

.cta-button {
  background: white;
  color: #2563eb;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-2px);
}

.video-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.video-button:hover {
  background: white;
  color: #2563eb;
}

/* Features Section */
.features {
  padding: 4rem 0;
  background: #f8fafc;
}

.dark-mode .features {
  background: #111827;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.feature-item {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.dark-mode .feature-item {
  background: #1f2937;
}

.feature-item:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-item h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1f2937;
}

.dark-mode .feature-item h3 {
  color: #f9fafb;
}

.feature-item p {
  color: #6b7280;
  margin-bottom: 1rem;
}

.dark-mode .feature-item p {
  color: #d1d5db;
}

.feature-images {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.feature-img-placeholder {
  width: 60px;
  height: 60px;
  background: #e5e7eb;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.dark-mode .feature-img-placeholder {
  background: #374151;
}

/* Languages Section */
.languages {
  padding: 4rem 0;
  background: white;
}

.dark-mode .languages {
  background: #1a1a1a;
}

.languages h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.dark-mode .languages h2 {
  color: #f9fafb;
}

.language-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.language-item {
  text-align: center;
  padding: 2rem;
  border-radius: 1rem;
  background: #f8fafc;
  transition: all 0.3s ease;
}

.dark-mode .language-item {
  background: #111827;
}

.language-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.language-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.language-item h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.dark-mode .language-item h3 {
  color: #f9fafb;
}

.language-item p {
  color: #6b7280;
}

.dark-mode .language-item p {
  color: #d1d5db;
}

/* Special Functions Section */
.special-functions {
  padding: 4rem 0;
  background: #f8fafc;
}

.dark-mode .special-functions {
  background: #111827;
}

.special-functions h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.dark-mode .special-functions h2 {
  color: #f9fafb;
}

.functions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.function-item {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.dark-mode .function-item {
  background: #1f2937;
}

.function-item:hover {
  transform: translateY(-5px);
}

.function-header {
  margin-bottom: 1rem;
}

.function-label {
  background: #2563eb;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.function-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.function-item h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1f2937;
}

.dark-mode .function-item h3 {
  color: #f9fafb;
}

.function-item p {
  color: #6b7280;
}

.dark-mode .function-item p {
  color: #d1d5db;
}

/* Exchanges Section */
.exchanges {
  padding: 4rem 0;
  background: white;
}

.dark-mode .exchanges {
  background: #1a1a1a;
}

.exchanges h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.dark-mode .exchanges h2 {
  color: #f9fafb;
}

.exchange-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
}

.exchange-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 1rem;
  transition: all 0.3s ease;
}

.dark-mode .exchange-item {
  background: #111827;
}

.exchange-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.exchange-logo {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.exchange-item span {
  font-weight: 600;
  color: #1f2937;
}

.dark-mode .exchange-item span {
  color: #f9fafb;
}

/* News Section */
.news {
  padding: 4rem 0;
  background: #f8fafc;
}

.dark-mode .news {
  background: #111827;
}

.news h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.dark-mode .news h2 {
  color: #f9fafb;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.news-item {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.dark-mode .news-item {
  background: #1f2937;
}

.news-item:hover {
  transform: translateY(-3px);
}

.news-date {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.dark-mode .news-date {
  color: #d1d5db;
}

.news-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.dark-mode .news-title {
  color: #f9fafb;
}

.news-content {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.dark-mode .news-content {
  color: #d1d5db;
}

.news-btn {
  background: #2563eb;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.news-btn:hover {
  background: #1d4ed8;
}

/* Footer */
.footer {
  background: #1f2937;
  color: white;
  padding: 3rem 0 2rem;
}

.footer-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: white;
}

.footer-links span {
  color: #6b7280;
}

.footer-disclaimer {
  text-align: center;
  color: #9ca3af;
  font-size: 0.875rem;
  line-height: 1.6;
}

.footer-disclaimer p {
  margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu {
    flex-wrap: wrap;
    justify-content: center;
  }

  .hero {
    padding: 6rem 1rem 3rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.5rem;
  }

  .hero-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .feature-grid,
  .language-grid,
  .functions-grid {
    grid-template-columns: 1fr;
  }

  .exchange-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer-links {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .exchange-grid {
    grid-template-columns: 1fr;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.25rem;
  }
}
</style>
