<script setup>
import { ref, onMounted } from 'vue'

const isDarkMode = ref(false)
const isMobileMenuOpen = ref(false)

const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value
  document.documentElement.classList.toggle('dark', isDarkMode.value)
}

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

onMounted(() => {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  }

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in')
      }
    })
  }, observerOptions)

  document.querySelectorAll('.animate-on-scroll').forEach(el => {
    observer.observe(el)
  })
})
</script>

<template>
  <div class="app" :class="{ 'dark': isDarkMode }">
    <!-- Navigation Header -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <div class="header-left">
            <div class="logo">
              <span class="logo-text">OpenQuant</span>
            </div>
            <nav class="nav-menu" :class="{ 'nav-menu-open': isMobileMenuOpen }">
              <a href="#" class="nav-link active">首页</a>
              <a href="#" class="nav-link">团队介绍</a>
              <a href="#" class="nav-link">价差分析</a>
              <a href="#" class="nav-link">可开分析</a>
              <a href="#" class="nav-link">API文档</a>
            </nav>
          </div>
          <div class="header-right">
            <button @click="toggleDarkMode" class="icon-btn">
              <span class="icon">🌙</span>
            </button>
            <button class="icon-btn">
              <span class="icon">🌐</span>
            </button>
            <button @click="toggleMobileMenu" class="mobile-menu-btn">
              <span class="hamburger"></span>
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
      <div class="container">
        <div class="hero-content animate-on-scroll">
          <h1 class="hero-title">
            专业的量化交易平台
          </h1>
          <p class="hero-subtitle">
            为专业交易者提供强大的量化交易工具和策略
          </p>
          <div class="hero-actions">
            <button class="btn-primary">免费试用</button>
            <button class="btn-video">
              <span class="video-icon">▶</span>
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features">
      <div class="container">
        <h2 class="section-title animate-on-scroll">核心功能</h2>

        <div class="feature-item animate-on-scroll">
          <div class="feature-content">
            <h3 class="feature-title">数据安全</h3>
            <p class="feature-description">银行级别的数据加密和安全保护</p>
            <div class="feature-images">
              <div class="feature-image-wrapper">
                <div class="feature-img">📊</div>
                <div class="feature-img-hover">📈</div>
              </div>
            </div>
          </div>
        </div>

        <div class="feature-item animate-on-scroll">
          <div class="feature-content">
            <h3 class="feature-title">安全保障</h3>
            <p class="feature-description">多重安全验证，保障资金安全</p>
            <div class="feature-images">
              <div class="feature-image-wrapper">
                <div class="feature-img">🛡️</div>
                <div class="feature-img-hover">🔐</div>
              </div>
            </div>
          </div>
        </div>

        <div class="feature-item animate-on-scroll">
          <div class="feature-content">
            <h3 class="feature-title">跨平台兼容性</h3>
            <p class="feature-description">策略支持多文件夹多文件</p>
            <div class="feature-images">
              <div class="feature-image-wrapper">
                <div class="feature-img">💻</div>
                <div class="feature-img-hover">📱</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Languages Section -->
    <section class="languages">
      <div class="container">
        <h2 class="section-title animate-on-scroll">支持的编程语言</h2>
        <div class="languages-grid">
          <div class="language-item animate-on-scroll">
            <div class="language-icon">
              <div class="lang-img">🐍</div>
            </div>
            <h3 class="language-name">Python</h3>
            <p class="language-desc">Python</p>
          </div>
          <div class="language-item animate-on-scroll">
            <div class="language-icon">
              <div class="lang-img">⚡</div>
            </div>
            <h3 class="language-name">C++</h3>
            <p class="language-desc">C++</p>
          </div>
          <div class="language-item animate-on-scroll">
            <div class="language-icon">
              <div class="lang-img">📗</div>
            </div>
            <h3 class="language-name">NodeJs</h3>
            <p class="language-desc">NodeJs</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Functions Section -->
    <section class="functions">
      <div class="container">
        <h2 class="section-title animate-on-scroll">功能模块</h2>
        <div class="functions-grid">
          <div class="function-card animate-on-scroll">
            <div class="function-label">Function 1</div>
            <div class="function-icon">
              <div class="func-icon">📊</div>
            </div>
            <h3 class="function-title">实盘管理</h3>
            <p class="function-desc">实盘监控 自定义列数据 便捷指令 批量操作</p>
          </div>

          <div class="function-card animate-on-scroll">
            <div class="function-label">Function 2</div>
            <div class="function-icon">
              <div class="func-icon">⚙️</div>
            </div>
            <h3 class="function-title">策略管理</h3>
            <p class="function-desc">多语言支持 代码加密上传 多文件 在线编辑</p>
          </div>

          <div class="function-card animate-on-scroll">
            <div class="function-label">Function 3</div>
            <div class="function-icon">
              <div class="func-icon">👥</div>
            </div>
            <h3 class="function-title">团队管理</h3>
            <p class="function-desc">策略共享 服务器共享 实盘共享 统一操控</p>
          </div>

          <div class="function-card animate-on-scroll">
            <div class="function-label">Function 4</div>
            <div class="function-icon">
              <div class="func-icon">✨</div>
            </div>
            <h3 class="function-title">更多功能</h3>
            <p class="function-desc">敬请期待</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Exchanges Section -->
    <section class="exchanges">
      <div class="container">
        <h2 class="section-title animate-on-scroll">支持的交易所</h2>
        <div class="exchanges-grid">
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">🔥</div>
            <span class="exchange-name">Huobi</span>
          </div>
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">⚡</div>
            <span class="exchange-name">Binance</span>
          </div>
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">🚀</div>
            <span class="exchange-name">Okex</span>
          </div>
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">🌟</div>
            <span class="exchange-name">Gate</span>
          </div>
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">💎</div>
            <span class="exchange-name">Mexc</span>
          </div>
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">⚡</div>
            <span class="exchange-name">BitMEX</span>
          </div>
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">🔷</div>
            <span class="exchange-name">Bybit</span>
          </div>
        </div>
      </div>
    </section>

    <!-- News Section -->
    <section class="news">
      <div class="container">
        <div class="news-header animate-on-scroll">
          <h2 class="section-title">最新资讯</h2>
          <div class="news-content">
            <h3 class="news-title">币安保本赚币上线RIF定期产品</h3>
            <p class="news-description">币安保本赚币平台将推出Rootstock Infrastructure Framework（RIF）定期产品专属活动！</p>
            <button class="news-btn">
              查看
              <span class="news-arrow">→</span>
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-links">
          <a href="#">关于我们</a>
          <span>|</span>
          <a href="#">服务条款</a>
          <span>|</span>
          <a href="#">隐私政策</a>
          <span>|</span>
          <a href="#">免责声明</a>
        </div>
        <div class="footer-disclaimer">
          <p>市场有风险，投资需谨慎 | 数据仅供参考，不构成投资建议</p>
          <p>Copyright © 2018-2024 OpenQuant Technology Co., Ltd. All Rights Reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
/* CSS Variables */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --white: #ffffff;
  --black: #000000;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-size-base: 1rem;
  --line-height-base: 1.5;
  --border-radius: 0.375rem;
  --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --transition: all 0.15s ease-in-out;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-sans-serif);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--gray-900);
  background-color: var(--white);
}

.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  letter-spacing: 1px;
}

.nav-menu {
  display: flex;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: var(--gray-700);
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-color);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.icon-btn {
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.icon-btn:hover {
  background: rgba(0, 123, 255, 0.1);
}

.icon {
  font-size: 1.2rem;
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
}

.hamburger {
  display: block;
  width: 25px;
  height: 3px;
  background: var(--gray-700);
  position: relative;
}

.hamburger::before,
.hamburger::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 3px;
  background: var(--gray-700);
  transition: var(--transition);
}

.hamburger::before {
  top: -8px;
}

.hamburger::after {
  bottom: -8px;
}

/* Hero Section */
.hero {
  padding: 8rem 0 4rem;
  text-align: center;
  color: white;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--border-radius);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

.btn-primary:hover {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-video {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 1rem;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.btn-video:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.video-icon {
  font-size: 1.2rem;
}

/* Section Styles */
section {
  padding: 4rem 0;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: white;
}

/* Features Section */
.features {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.feature-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition);
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.feature-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
}

.feature-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
}

.feature-images {
  display: flex;
  gap: 1rem;
}

.feature-image-wrapper {
  position: relative;
  width: 60px;
  height: 60px;
}

.feature-img,
.feature-img-hover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.feature-img-hover {
  opacity: 0;
  transform: scale(0.8);
}

.feature-item:hover .feature-img {
  opacity: 0;
  transform: scale(1.2);
}

.feature-item:hover .feature-img-hover {
  opacity: 1;
  transform: scale(1);
}

/* Languages Section */
.languages {
  background: rgba(255, 255, 255, 0.05);
}

.languages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.language-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius);
  padding: 2rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition);
}

.language-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.language-icon {
  margin-bottom: 1rem;
}

.lang-img {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.language-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
}

.language-desc {
  color: rgba(255, 255, 255, 0.8);
}

/* Functions Section */
.functions {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.functions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.function-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius);
  padding: 2rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.function-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.function-label {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 123, 255, 0.8);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.function-icon {
  margin-bottom: 1.5rem;
}

.func-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.function-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
}

.function-desc {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

/* Exchanges Section */
.exchanges {
  background: rgba(255, 255, 255, 0.05);
}

.exchanges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
}

.exchange-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.exchange-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.exchange-logo {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.exchange-name {
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
}

/* News Section */
.news {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.news-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius);
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.news-content {
  margin-top: 2rem;
}

.news-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
}

.news-description {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.news-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.news-btn:hover {
  background: #0056b3;
  transform: translateX(5px);
}

.news-arrow {
  transition: var(--transition);
}

.news-btn:hover .news-arrow {
  transform: translateX(3px);
}

/* Footer */
.footer {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  padding: 2rem 0;
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: var(--transition);
}

.footer-links a:hover {
  color: white;
}

.footer-links span {
  color: rgba(255, 255, 255, 0.5);
}

.footer-disclaimer {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  line-height: 1.6;
}

.footer-disclaimer p {
  margin-bottom: 0.5rem;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.animate-on-scroll.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    flex-direction: column;
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .nav-menu-open {
    display: flex;
  }

  .mobile-menu-btn {
    display: block;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .feature-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .functions-grid {
    grid-template-columns: 1fr;
  }

  .languages-grid {
    grid-template-columns: 1fr;
  }

  .exchanges-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer-links {
    flex-direction: column;
    gap: 0.5rem;
  }

  .footer-links span {
    display: none;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .hero {
    padding: 6rem 0 3rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .exchanges-grid {
    grid-template-columns: 1fr;
  }
}

/* Dark Mode Support */
.dark {
  --primary-color: #4dabf7;
  --gray-900: #f8f9fa;
  --gray-700: #adb5bd;
}

.dark .header {
  background: rgba(0, 0, 0, 0.95);
}

.dark .nav-link {
  color: rgba(255, 255, 255, 0.8);
}

.dark .nav-link:hover,
.dark .nav-link.active {
  color: var(--primary-color);
}

/* Print Styles */
@media print {
  .header,
  .footer {
    display: none;
  }

  .app {
    background: white;
    color: black;
  }

  section {
    page-break-inside: avoid;
  }
}
</style>
