<script setup>
import { ref, onMounted } from 'vue'

const isDarkMode = ref(false)
const isMobileMenuOpen = ref(false)

const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value
  document.documentElement.classList.toggle('dark', isDarkMode.value)
}

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const newsItems = [
  {
    date: '2025-04-10',
    title: '币安保本赚币上线RIF定期产品',
    content: '币安保本赚币平台将推出Rootstock Infrastructure Framework（RIF）定期产品专属活动！活动期间申购RIF定期产品的用户，享最高9.9%*年化收益率。'
  },
  {
    date: '2025-04-10',
    title: '关于调整现货交易对最小数量波动的公告',
    content: '为提升市场的有效流动性和交易体验，币安将分别于2025年04月17日15:00（东八区时间）前，完成部分现货交易对的最小数量波动（单位变动的最小值）调整，'
  },
  {
    date: '2025-04-09',
    title: '币安将推出LDUSDT合约交易，并提供年化回报率（APR）奖励',
    content: '币安合约即将推出新的收益型保证金资产——LDUSDT。用户可以通过该新型资产将其USDT保本赚币的活期产品资产兑换为LDUSDT，并将其用作U本位合约的保证金。此外，持有LDUSDT的用户将能够从保本赚币活期产品中继续获得实时的年化回报率（APR）奖励。'
  }
]

onMounted(() => {
  // 添加滚动动画
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  }

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in')
      }
    })
  }, observerOptions)

  // 观察所有需要动画的元素
  document.querySelectorAll('.animate-on-scroll').forEach(el => {
    observer.observe(el)
  })
})
</script>

<template>
  <div class="app">
    <!-- Navigation Header -->
    <header class="header" :class="{ 'header-scrolled': false }">
      <div class="header-container">
        <div class="header-left">
          <div class="logo">
            <span class="logo-text">OpenQuant</span>
          </div>
          <nav class="nav-menu" :class="{ 'nav-menu-open': isMobileMenuOpen }">
            <a href="#" class="nav-link active">首页</a>
            <a href="#" class="nav-link">团队介绍</a>
            <a href="#" class="nav-link">价差分析</a>
            <a href="#" class="nav-link">可开分析</a>
            <a href="#" class="nav-link">API文档</a>
          </nav>
        </div>
        <div class="header-right">
          <button @click="toggleDarkMode" class="icon-btn">
            <span class="icon">🌙</span>
          </button>
          <button class="icon-btn">
            <span class="icon">🌐</span>
          </button>
          <button class="login-btn">登录</button>
          <button @click="toggleMobileMenu" class="mobile-menu-btn">
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>
      </div>
    </header>

    <!-- Mobile Menu -->
    <div class="mobile-menu" :class="{ 'mobile-menu-open': isMobileMenuOpen }">
      <nav class="mobile-nav">
        <a href="#" class="mobile-nav-link">首页</a>
        <a href="#" class="mobile-nav-link">团队介绍</a>
        <a href="#" class="mobile-nav-link">价差分析</a>
        <a href="#" class="mobile-nav-link">可开分析</a>
        <a href="#" class="mobile-nav-link">API文档</a>
      </nav>
    </div>

    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-bg">
        <div class="hero-particles"></div>
      </div>
      <div class="hero-container">
        <div class="hero-content animate-on-scroll">
          <h1 class="hero-title">
            <span class="hero-title-main">OPENQUANT</span>
          </h1>
          <h2 class="hero-subtitle">开启非凡量化世界者</h2>
          <p class="hero-description">交易策略开发、量化学习资源、策略出租出售</p>
          <div class="hero-actions">
            <button class="btn-primary">免费试用</button>
            <button class="btn-video">
              <span class="video-icon">▶</span>
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features">
      <div class="features-container">
        <div class="feature-item animate-on-scroll">
          <div class="feature-content">
            <h3 class="feature-title">多文档在线编辑</h3>
            <div class="feature-images">
              <div class="feature-image-wrapper">
                <div class="feature-img">📊</div>
                <div class="feature-img-hover">📈</div>
              </div>
            </div>
          </div>
        </div>

        <div class="feature-item animate-on-scroll">
          <div class="feature-content">
            <h3 class="feature-title">数据双重加密</h3>
            <p class="feature-description">双重加密机制确保了数据不泄露， 即便数据被</p>
            <div class="feature-images">
              <div class="feature-image-wrapper">
                <div class="feature-img">🛡️</div>
                <div class="feature-img-hover">🔐</div>
              </div>
            </div>
          </div>
        </div>

        <div class="feature-item animate-on-scroll">
          <div class="feature-content">
            <h3 class="feature-title">跨平台兼容性</h3>
            <p class="feature-description">策略支持多文件夹多文件</p>
            <div class="feature-images">
              <div class="feature-image-wrapper">
                <div class="feature-img">💻</div>
                <div class="feature-img-hover">📱</div>
              </div>
            </div>
          </div>
        </div>

        <div class="feature-item animate-on-scroll">
          <div class="feature-content">
            <h3 class="feature-title">实时协作</h3>
            <p class="feature-description">多位用户可以同时编辑同一文档或代码，即时看到彼此的更改，极大地提升了团队协作的‌‌ 效率和灵活性</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Programming Languages Section -->
    <section class="languages">
      <div class="languages-container">
        <h2 class="section-title animate-on-scroll">支持丰富的编程语言</h2>
        <div class="languages-grid">
          <div class="language-item animate-on-scroll">
            <div class="language-icon">
              <div class="lang-img">🐍</div>
            </div>
            <h3 class="language-name">Python</h3>
            <p class="language-desc">Python</p>
          </div>
          <div class="language-item animate-on-scroll">
            <div class="language-icon">
              <div class="lang-img">⚡</div>
            </div>
            <h3 class="language-name">C++</h3>
            <p class="language-desc">C++</p>
          </div>
          <div class="language-item animate-on-scroll">
            <div class="language-icon">
              <div class="lang-img">📗</div>
            </div>
            <h3 class="language-name">NodeJs</h3>
            <p class="language-desc">NodeJs</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Special Functions Section -->
    <section class="functions">
      <div class="functions-container">
        <h2 class="section-title animate-on-scroll">特色功能</h2>
        <div class="functions-grid">
          <div class="function-card animate-on-scroll">
            <div class="function-label">Function 1</div>
            <div class="function-icon">
              <div class="func-icon">📊</div>
            </div>
            <h3 class="function-title">实盘管理</h3>
            <p class="function-desc">实盘监控 自定义列数据 便捷指令 批量操作</p>
          </div>

          <div class="function-card animate-on-scroll">
            <div class="function-label">Function 2</div>
            <div class="function-icon">
              <div class="func-icon">⚙️</div>
            </div>
            <h3 class="function-title">策略管理</h3>
            <p class="function-desc">多语言支持 代码加密上传 多文件 在线编辑</p>
          </div>

          <div class="function-card animate-on-scroll">
            <div class="function-label">Function 3</div>
            <div class="function-icon">
              <div class="func-icon">👥</div>
            </div>
            <h3 class="function-title">团队管理</h3>
            <p class="function-desc">策略共享 服务器共享 实盘共享 统一操控</p>
          </div>

          <div class="function-card animate-on-scroll">
            <div class="function-label">Function 4</div>
            <div class="function-icon">
              <div class="func-icon">✨</div>
            </div>
            <h3 class="function-title">更多功能</h3>
            <p class="function-desc">敬请期待</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Exchanges Section -->
    <section class="exchanges">
      <div class="exchanges-container">
        <h2 class="section-title animate-on-scroll">全面的交易所对接</h2>
        <div class="exchanges-grid">
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">🔥</div>
            <span class="exchange-name">Huobi</span>
          </div>
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">⚡</div>
            <span class="exchange-name">Binance</span>
          </div>
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">🚀</div>
            <span class="exchange-name">Okex</span>
          </div>
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">🌟</div>
            <span class="exchange-name">Gate</span>
          </div>
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">💎</div>
            <span class="exchange-name">Mexc</span>
          </div>
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">⚡</div>
            <span class="exchange-name">BitMEX</span>
          </div>
          <div class="exchange-item animate-on-scroll">
            <div class="exchange-logo">🔷</div>
            <span class="exchange-name">Bybit</span>
          </div>
        </div>
      </div>
    </section>

    <!-- News Section -->
    <section class="news">
      <div class="news-container">
        <h2 class="section-title animate-on-scroll">热点资讯</h2>
        <div class="news-list">
          <div v-for="(item, index) in newsItems" :key="index" class="news-card animate-on-scroll">
            <div class="news-date">{{ item.date }}</div>
            <h3 class="news-title">{{ item.title }}</h3>
            <p class="news-content">{{ item.content }}</p>
            <button class="news-btn">
              查看
              <span class="news-arrow">→</span>
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-links">
          <a href="#">关于我们</a>
          <span>|</span>
          <a href="#">服务条款</a>
          <span>|</span>
          <a href="#">隐私政策</a>
          <span>|</span>
          <a href="#">免责声明</a>
        </div>
        <div class="footer-disclaimer">
          <p>市场有风险，投资需谨慎 | 数据仅供参考，不构成投资建议</p>
          <p>Copyright © 2018-2024 OpenQuant Technology Co., Ltd. All Rights Reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
  overflow-x: hidden;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-on-scroll.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Header */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.header-scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 3rem;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: #007bff;
  letter-spacing: 1px;
}

.nav-menu {
  display: flex;
  gap: 2rem;
  list-style: none;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
  padding: 8px 0;
}

.nav-link:hover,
.nav-link.active {
  color: #007bff;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #007bff;
  border-radius: 1px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.icon-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

.icon {
  font-size: 18px;
}

.login-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.login-btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.mobile-menu-btn {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  gap: 3px;
}

.mobile-menu-btn span {
  width: 20px;
  height: 2px;
  background: #333;
  transition: all 0.3s ease;
}

/* Mobile Menu */
.mobile-menu {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transform: translateY(-100%);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 999;
}

.mobile-menu-open {
  transform: translateY(0);
  opacity: 1;
}

.mobile-nav {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mobile-nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  padding: 10px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  overflow: hidden;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

.hero-container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  margin-bottom: 2rem;
}

.hero-title-main {
  font-size: 4.5rem;
  font-weight: 700;
  letter-spacing: 3px;
  background: linear-gradient(45deg, #fff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: pulse 3s ease-in-out infinite;
}

.hero-subtitle {
  font-size: 2rem;
  font-weight: 300;
  margin-bottom: 1.5rem;
  opacity: 0.95;
  letter-spacing: 1px;
}

.hero-description {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  opacity: 0.9;
  line-height: 1.8;
}

.hero-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.btn-primary {
  background: linear-gradient(45deg, #fff, #f8fafc);
  color: #667eea;
  border: none;
  padding: 15px 35px;
  border-radius: 30px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.btn-video {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  width: 70px;
  height: 70px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.btn-video:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

.video-icon {
  font-size: 20px;
  color: white;
}

/* Features Section */
.features {
  padding: 100px 0;
  background: #f8fafc;
  position: relative;
}

.features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 3rem;
}

.feature-item {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.feature-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.feature-item:hover::before {
  transform: scaleX(1);
}

.feature-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.feature-content {
  text-align: center;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 1.5rem;
  line-height: 1.4;
}

.feature-description {
  color: #64748b;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.feature-images {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.feature-image-wrapper {
  position: relative;
  width: 200px;
  height: 120px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-img {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.feature-img-hover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 8px;
  opacity: 0;
  transition: all 0.3s ease;
}

.feature-image-wrapper:hover .feature-img {
  opacity: 0;
}

.feature-image-wrapper:hover .feature-img-hover {
  opacity: 1;
}

/* Section Title */
.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4rem;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
}

/* Languages Section */
.languages {
  padding: 100px 0;
  background: white;
}

.languages-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.languages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
}

.language-item {
  text-align: center;
  padding: 3rem 2rem;
  border-radius: 20px;
  background: linear-gradient(145deg, #f8fafc, #e2e8f0);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.language-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.language-item:hover::before {
  opacity: 1;
}

.language-item:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.language-icon {
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.lang-img {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.language-item:hover .lang-img {
  transform: scale(1.1) rotate(5deg);
}

.language-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 2;
}

.language-desc {
  color: #64748b;
  font-size: 1rem;
  position: relative;
  z-index: 2;
}

/* Functions Section */
.functions {
  padding: 100px 0;
  background: #f8fafc;
}

.functions-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.functions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2.5rem;
}

.function-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.function-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.function-card:hover::before {
  opacity: 1;
}

.function-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.function-label {
  display: inline-block;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 2;
}

.function-icon {
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 2;
}

.func-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.function-card:hover .func-icon {
  transform: scale(1.1);
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.function-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
}

.function-desc {
  color: #64748b;
  line-height: 1.6;
  position: relative;
  z-index: 2;
}

/* Exchanges Section */
.exchanges {
  padding: 100px 0;
  background: white;
}

.exchanges-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.exchanges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
}

.exchange-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem 1rem;
  background: #f8fafc;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
}

.exchange-item:hover {
  transform: translateY(-8px);
  background: white;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.exchange-logo {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.exchange-item:hover .exchange-logo {
  transform: scale(1.1);
}

.exchange-name {
  font-weight: 600;
  color: #1a202c;
  font-size: 0.9rem;
}

/* News Section */
.news {
  padding: 100px 0;
  background: #f8fafc;
}

.news-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.news-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.news-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.news-card:hover::before {
  transform: scaleX(1);
}

.news-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.news-date {
  color: #64748b;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.news-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.news-content {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.news-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: fit-content;
}

.news-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.news-arrow {
  font-size: 16px;
  margin-left: 0.25rem;
}

/* Footer */
.footer {
  background: #1a202c;
  color: white;
  padding: 3rem 0 2rem;
}

.footer-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: #cbd5e0;
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 0.9rem;
}

.footer-links a:hover {
  color: white;
}

.footer-links span {
  color: #4a5568;
}

.footer-disclaimer {
  text-align: center;
  color: #718096;
  font-size: 0.875rem;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

.footer-disclaimer p {
  margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-title-main {
    font-size: 3.5rem;
  }

  .features-container,
  .languages-grid,
  .functions-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }

  .exchanges-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
}

@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .header-container {
    height: 60px;
  }

  .hero {
    min-height: 80vh;
    padding: 2rem 0;
  }

  .hero-title-main {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.5rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-actions {
    flex-direction: column;
    gap: 1.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .features-container,
  .languages-grid,
  .functions-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .exchanges-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .feature-item,
  .language-item,
  .function-card,
  .news-card {
    padding: 2rem;
  }

  .footer-links {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 15px;
  }

  .hero-container,
  .features-container,
  .languages-container,
  .functions-container,
  .exchanges-container,
  .news-container {
    padding: 0 15px;
  }

  .hero-title-main {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.25rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .exchanges-grid {
    grid-template-columns: 1fr;
  }

  .feature-item,
  .language-item,
  .function-card,
  .news-card {
    padding: 1.5rem;
  }

  .btn-primary {
    padding: 12px 25px;
    font-size: 1rem;
  }

  .btn-video {
    width: 60px;
    height: 60px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .app {
    background-color: #1a202c;
    color: #e2e8f0;
  }

  .header {
    background: rgba(26, 32, 44, 0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .nav-link {
    color: #e2e8f0;
  }

  .mobile-menu {
    background: #1a202c;
  }

  .mobile-nav-link {
    color: #e2e8f0;
  }

  .features {
    background: #2d3748;
  }

  .feature-item,
  .function-card,
  .news-card {
    background: #1a202c;
    color: #e2e8f0;
  }

  .feature-title,
  .function-title,
  .news-title,
  .section-title {
    color: #f7fafc;
  }

  .languages {
    background: #1a202c;
  }

  .language-item {
    background: linear-gradient(145deg, #2d3748, #4a5568);
  }

  .exchanges {
    background: #1a202c;
  }

  .exchange-item {
    background: #2d3748;
  }

  .exchange-item:hover {
    background: #4a5568;
  }

  .news {
    background: #2d3748;
  }
}

/* Print styles */
@media print {
  .header,
  .mobile-menu,
  .btn-primary,
  .btn-video,
  .news-btn {
    display: none;
  }

  .hero {
    background: none;
    color: black;
    min-height: auto;
    padding: 2rem 0;
  }

  .section-title {
    color: black;
  }
}
</style>

/* Features Section */
.features {
  padding: 4rem 0;
  background: #f8fafc;
}

.dark-mode .features {
  background: #111827;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.feature-item {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.dark-mode .feature-item {
  background: #1f2937;
}

.feature-item:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-item h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1f2937;
}

.dark-mode .feature-item h3 {
  color: #f9fafb;
}

.feature-item p {
  color: #6b7280;
  margin-bottom: 1rem;
}

.dark-mode .feature-item p {
  color: #d1d5db;
}

.feature-images {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.feature-img-placeholder {
  width: 60px;
  height: 60px;
  background: #e5e7eb;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.dark-mode .feature-img-placeholder {
  background: #374151;
}

/* Languages Section */
.languages {
  padding: 4rem 0;
  background: white;
}

.dark-mode .languages {
  background: #1a1a1a;
}

.languages h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.dark-mode .languages h2 {
  color: #f9fafb;
}

.language-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.language-item {
  text-align: center;
  padding: 2rem;
  border-radius: 1rem;
  background: #f8fafc;
  transition: all 0.3s ease;
}

.dark-mode .language-item {
  background: #111827;
}

.language-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.language-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.language-item h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.dark-mode .language-item h3 {
  color: #f9fafb;
}

.language-item p {
  color: #6b7280;
}

.dark-mode .language-item p {
  color: #d1d5db;
}

/* Special Functions Section */
.special-functions {
  padding: 4rem 0;
  background: #f8fafc;
}

.dark-mode .special-functions {
  background: #111827;
}

.special-functions h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.dark-mode .special-functions h2 {
  color: #f9fafb;
}

.functions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.function-item {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.dark-mode .function-item {
  background: #1f2937;
}

.function-item:hover {
  transform: translateY(-5px);
}

.function-header {
  margin-bottom: 1rem;
}

.function-label {
  background: #2563eb;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.function-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.function-item h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1f2937;
}

.dark-mode .function-item h3 {
  color: #f9fafb;
}

.function-item p {
  color: #6b7280;
}

.dark-mode .function-item p {
  color: #d1d5db;
}

/* Exchanges Section */
.exchanges {
  padding: 4rem 0;
  background: white;
}

.dark-mode .exchanges {
  background: #1a1a1a;
}

.exchanges h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.dark-mode .exchanges h2 {
  color: #f9fafb;
}

.exchange-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
}

.exchange-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 1rem;
  transition: all 0.3s ease;
}

.dark-mode .exchange-item {
  background: #111827;
}

.exchange-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.exchange-logo {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.exchange-item span {
  font-weight: 600;
  color: #1f2937;
}

.dark-mode .exchange-item span {
  color: #f9fafb;
}

/* News Section */
.news {
  padding: 4rem 0;
  background: #f8fafc;
}

.dark-mode .news {
  background: #111827;
}

.news h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1f2937;
}

.dark-mode .news h2 {
  color: #f9fafb;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.news-item {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.dark-mode .news-item {
  background: #1f2937;
}

.news-item:hover {
  transform: translateY(-3px);
}

.news-date {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.dark-mode .news-date {
  color: #d1d5db;
}

.news-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.dark-mode .news-title {
  color: #f9fafb;
}

.news-content {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.dark-mode .news-content {
  color: #d1d5db;
}

.news-btn {
  background: #2563eb;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.news-btn:hover {
  background: #1d4ed8;
}

/* Footer */
.footer {
  background: #1f2937;
  color: white;
  padding: 3rem 0 2rem;
}

.footer-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: white;
}

.footer-links span {
  color: #6b7280;
}

.footer-disclaimer {
  text-align: center;
  color: #9ca3af;
  font-size: 0.875rem;
  line-height: 1.6;
}

.footer-disclaimer p {
  margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu {
    flex-wrap: wrap;
    justify-content: center;
  }

  .hero {
    padding: 6rem 1rem 3rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.5rem;
  }

  .hero-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .feature-grid,
  .language-grid,
  .functions-grid {
    grid-template-columns: 1fr;
  }

  .exchange-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer-links {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .exchange-grid {
    grid-template-columns: 1fr;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.25rem;
  }
}
</style>
